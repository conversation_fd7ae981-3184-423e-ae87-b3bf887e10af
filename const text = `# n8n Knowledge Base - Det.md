const text = `# n8n Knowledge Base - Detaillierte Dokumentation für KI-Assistenten

## 1. Grundlage<PERSON> von n8n - Detailliert

### Was ist n8n?
n8n (ausgesprochen "n-eight-n") ist eine visuelle Workflow-Automatisierungsplattform mit folgenden Kerneigenschaften:
- **Fair-Code Lizenz**: Open Source mit kommerziellen Einschränkungen
- **400+ Integrationen**: Vorgefertigte Nodes für APIs und Services
- **Visueller Editor**: Drag-and-Drop Interface
- **Code-Unterstützung**: JavaScript und Python für Custom Logic
- **Self-Hosting**: Vollständige Kontrolle über Daten und Infrastruktur

### Datenstrukturen in n8n

#### Items (Grundlegende Dateneinheit)
```json
{
  "json": {
    "id": 123,
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "binary": {},
  "pairedItem": {
    "item": 0
  }
}
```

#### Datenfluss zwischen Nodes
- Jeder Node gibt ein Array von Items aus
- Items werden als JSON-Objekte übertragen
- Binary Data wird separat gespeichert
- Connections übertragen komplette Item-Arrays

## 2. Detaillierte Node-Kategorien und Konfigurationen

### 2.1 Trigger Nodes - Vollständige Konfiguration

#### Manual Trigger
**Zweck**: Manueller Workflow-Start für Testing
**Konfiguration**: Keine Parameter erforderlich
**Output**: Leeres Item für Workflow-Start
```json
{
  "json": {}
}
```

#### Schedule Trigger
**Zweck**: Zeitbasierte Workflow-Ausführung
**Parameter**:
- `interval`: Ausführungsintervall
  - `seconds`: 1-59 Sekunden
  - `minutes`: 1-59 Minuten  
  - `hours`: 1-23 Stunden
  - `days`: 1-31 Tage
  - `weeks`: 1-4 Wochen
  - `months`: 1-12 Monate
- `field`: Spezifische Zeit (bei hours/days)

**Beispiel-Konfiguration**:
```json
{
  "interval": "hours",
  "field": "2",
  "triggerAtStartup": true
}
```

#### Webhook
**Zweck**: HTTP-Endpunkt für externe API-Calls
**Parameter**:
- `path`: URL-Pfad (z.B. "webhook-test")
- `httpMethod`: GET, POST, PUT, DELETE, PATCH
- `authentication`: none, basicAuth, headerAuth
- `responseMode`: onReceived, lastNode, responseCode

**Vollständige Konfiguration**:
```json
{
  "path": "my-webhook",
  "httpMethod": "POST",
  "authentication": "none",
  "responseMode": "onReceived",
  "responseCode": 200,
  "responseData": "success"
}
```

**Webhook Output-Beispiel**:
```json
{
  "json": {
    "headers": {
      "content-type": "application/json",
      "user-agent": "curl/7.68.0"
    },
    "params": {},
    "query": {},
    "body": {
      "name": "Test",
      "value": 123
    }
  }
}
```

### 2.2 Core Logic Nodes - Detaillierte Konfiguration

#### IF Node
**Zweck**: Bedingte Verzweigung mit True/False Outputs
**Parameter**:
- `conditions`: Array von Bedingungen
  - `leftValue`: Wert oder Expression
  - `operation`: equals, notEquals, larger, smaller, contains, etc.
  - `rightValue`: Vergleichswert
- `combineOperation`: and, or (bei mehreren Bedingungen)

**Vollständige IF-Konfiguration**:
```json
{
  "conditions": {
    "options": {
      "caseSensitive": true,
      "leftValue": "",
      "typeValidation": "strict"
    },
    "conditions": [
      {
        "leftValue": "={{ $json.status }}",
        "rightValue": "active",
        "operator": {
          "type": "string",
          "operation": "equals"
        }
      }
    ],
    "combineOperation": "and"
  }
}
```

**Praktische IF-Beispiele**:

1. **String-Vergleich**:
```json
{
  "leftValue": "={{ $json.email }}",
  "operation": "contains",
  "rightValue": "@gmail.com"
}
```

2. **Numerischer Vergleich**:
```json
{
  "leftValue": "={{ $json.age }}",
  "operation": "larger",
  "rightValue": 18
}
```

3. **Boolean-Check**:
```json
{
  "leftValue": "={{ $json.isActive }}",
  "operation": "equals",
  "rightValue": true
}
```

#### Switch Node
**Zweck**: Mehrfache Verzweigung basierend auf Werten
**Modi**:
- `rules`: Bedingungsbasiert
- `expression`: JavaScript Expression

**Rules-Modus Konfiguration**:
```json
{
  "mode": "rules",
  "rules": {
    "rules": [
      {
        "operation": "equal",
        "value1": "={{ $json.category }}",
        "value2": "premium",
        "output": 0
      },
      {
        "operation": "equal", 
        "value1": "={{ $json.category }}",
        "value2": "standard",
        "output": 1
      }
    ],
    "fallbackOutput": 2
  }
}
```

**Expression-Modus**:
```json
{
  "mode": "expression",
  "expression": "={{ $json.priority }}",
  "outputs": [
    { "value": "high" },
    { "value": "medium" },
    { "value": "low" }
  ]
}
```

#### Code Node - Detaillierte JavaScript-Verwendung
**Zweck**: Custom JavaScript/Python Code für komplexe Logik

**Verfügbare Objekte in JavaScript**:
- `$input`: Input-Daten-Objekt
- `items`: Array aller Input-Items
- `$json`: Aktuelles JSON-Objekt (in item loops)
- `$binary`: Binary-Daten des aktuellen Items
- `$itemIndex`: Index des aktuellen Items
- `$now`: Aktueller Zeitstempel
- `$today`: Heutiges Datum
- `$workflow`: Workflow-Metadaten
- `$execution`: Execution-Informationen

**Code Node Return-Format**:
```javascript
// Muss IMMER Array von Items zurückgeben
return [
  {
    json: {
      result: "processed data",
      timestamp: new Date().toISOString()
    }
  }
];
```

**Praktische Code-Beispiele**:

1. **Datenverarbeitung**:
```javascript
// Alle Items verarbeiten
const processedItems = items.map((item, index) => ({
  json: {
    ...item.json,
    processed: true,
    processedAt: $now,
    originalIndex: index
  }
}));

return processedItems;
```

2. **Datenfilterung**:
```javascript
// Nur aktive Benutzer zurückgeben
const activeUsers = items.filter(item => 
  item.json.status === 'active' && 
  item.json.lastLogin > Date.now() - (30 * 24 * 60 * 60 * 1000)
);

return activeUsers;
```

3. **Datenaggregation**:
```javascript
// Summierung und Gruppierung
const summary = items.reduce((acc, item) => {
  const category = item.json.category;
  if (!acc[category]) {
    acc[category] = { count: 0, total: 0 };
  }
  acc[category].count++;
  acc[category].total += item.json.amount;
  return acc;
}, {});

return [{ json: summary }];
```

4. **API-Datenverarbeitung**:
```javascript
// API Response verarbeiten
const apiData = items[0].json;
const transformedData = {
  users: apiData.data.map(user => ({
    id: user.id,
    name: `${user.first_name} ${user.last_name}`,
    email: user.email.toLowerCase(),
    createdAt: new Date(user.created_at).toISOString()
  })),
  meta: {
    total: apiData.total,
    processedAt: $now
  }
};

return [{ json: transformedData }];
```

#### Set Node (Edit Fields)
**Zweck**: Daten setzen, modifizieren oder entfernen
**Modi**:
- `keepOnlySet`: Nur gesetzte Felder behalten
- `addToAll`: Zu allen Items hinzufügen
- `manual`: Manuelle Feldkonfiguration

**Vollständige Set-Konfiguration**:
```json
{
  "mode": "manual",
  "duplicateItem": false,
  "includeOtherFields": true,
  "assignments": {
    "assignments": [
      {
        "id": "field1",
        "name": "fullName",
        "type": "string",
        "value": "={{ $json.firstName + ' ' + $json.lastName }}"
      },
      {
        "id": "field2", 
        "name": "isAdult",
        "type": "boolean",
        "value": "={{ $json.age >= 18 }}"
      },
      {
        "id": "field3",
        "name": "tags",
        "type": "array",
        "value": "={{ $json.interests.split(',') }}"
      }
    ]
  }
}
```

**Set Node Feld-Typen**:
- `string`: Text-Werte
- `number`: Numerische Werte
- `boolean`: True/False
- `array`: Arrays
- `object`: JSON-Objekte
- `null`: Null-Werte

### 2.3 HTTP Request Node - Vollständige API-Integration

**Parameter-Übersicht**:
- `method`: HTTP-Methode
- `url`: Ziel-URL
- `authentication`: Authentifizierung
- `sendHeaders`: Custom Headers
- `sendBody`: Request Body
- `sendQuery`: Query Parameter
- `options`: Erweiterte Optionen

**Vollständige HTTP-Konfiguration**:
```json
{
  "method": "POST",
  "url": "https://api.example.com/users",
  "authentication": "genericCredentialType",
  "sendHeaders": true,
  "headerParameters": {
    "parameters": [
      {
        "name": "Content-Type",
        "value": "application/json"
      },
      {
        "name": "User-Agent", 
        "value": "n8n-workflow/1.0"
      }
    ]
  },
  "sendBody": true,
  "bodyParameters": {
    "parameters": [
      {
        "name": "name",
        "value": "={{ $json.fullName }}"
      },
      {
        "name": "email",
        "value": "={{ $json.email }}"
      }
    ]
  },
  "sendQuery": true,
  "queryParameters": {
    "parameters": [
      {
        "name": "format",
        "value": "json"
      }
    ]
  },
  "options": {
    "timeout": 30000,
    "retry": {
      "enabled": true,
      "maxRetries": 3
    },
    "response": {
      "response": {
        "fullResponse": false,
        "neverError": false
      }
    }
  }
}
```

**HTTP Authentication-Typen**:

1. **Basic Auth**:
```json
{
  "authentication": "basicAuth",
  "basicAuth": {
    "user": "username",
    "password": "password"
  }
}
```

2. **Bearer Token**:
```json
{
  "authentication": "headerAuth",
  "headerAuth": {
    "name": "Authorization",
    "value": "Bearer {{ $credentials.token }}"
  }
}
```

3. **API Key**:
```json
{
  "authentication": "headerAuth", 
  "headerAuth": {
    "name": "X-API-Key",
    "value": "{{ $credentials.apiKey }}"
  }
}
```

### 2.4 Merge Node - Datenvereinigung

**Modi**:
- `append`: Daten anhängen
- `pass`: Nur Input 1 durchlassen
- `wait`: Auf beide Inputs warten
- `merge`: Items basierend auf Key mergen

**Append-Modus**:
```json
{
  "mode": "append",
  "joinMode": "waitForAll",
  "outputDataFrom": "all"
}
```

**Merge-by-Key Konfiguration**:
```json
{
  "mode": "merge",
  "mergeByFields": {
    "values": [
      {
        "field1": "id",
        "field2": "userId"
      }
    ]
  },
  "joinMode": "inner",
  "outputDataFrom": "both"
}
```

## 3. Expression System - Vollständige Referenz

### 3.1 Basis Expression-Syntax

**Grundlegende Syntax**:
- `{{ expression }}`: Standard Expression
- `=$json.field`: Vereinfachte Syntax
- Expressions werden zur Laufzeit evaluiert

### 3.2 Datenzugriff-Expressions

**Aktueller Node-Daten**:
```javascript
// JSON-Felder
{{ $json.fieldName }}
{{ $json.user.email }}
{{ $json.items[0].name }}

// Binary-Daten
{{ $binary.fileName }}

// Item-Index
{{ $itemIndex }}
```

**Andere Node-Daten**:
```javascript
// Spezifischer Node
{{ $('Node Name').item.json.field }}
{{ $('HTTP Request').first().json.response }}

// Alle Items eines Nodes
{{ $('Node Name').all() }}

// Letzter Node
{{ $input.item.json.field }}
{{ $input.all() }}
```

### 3.3 Built-in Funktionen und Variablen

**Datum und Zeit**:
```javascript
// Aktuelle Zeit
{{ $now }}                          // 2024-01-15T10:30:00.000Z
{{ new Date().toISOString() }}      // ISO String
{{ DateTime.now().toISO() }}        // Luxon DateTime

// Datum-Manipulation
{{ DateTime.now().plus({days: 7}).toISO() }}     // +7 Tage
{{ DateTime.now().minus({hours: 2}).toISO() }}   // -2 Stunden
{{ DateTime.now().startOf('day').toISO() }}      // Tagesbeginn

// Formatierung
{{ DateTime.now().toFormat('yyyy-MM-dd') }}      // 2024-01-15
{{ DateTime.now().toFormat('HH:mm:ss') }}        // 10:30:00
```

**String-Manipulation**:
```javascript
// Basis-Operationen
{{ $json.name.toUpperCase() }}
{{ $json.email.toLowerCase() }}
{{ $json.text.trim() }}

// Erweiterte String-Funktionen
{{ $json.name.replace('old', 'new') }}
{{ $json.text.split(' ') }}
{{ $json.words.join(', ') }}
{{ $json.text.substring(0, 10) }}
{{ $json.email.includes('@gmail.com') }}

// RegEx
{{ $json.phone.replace(/[^\d]/g, '') }}          // Nur Zahlen
{{ /^\w+@\w+\.\w+$/.test($json.email) }}        // Email-Validation
```

**Numerische Operationen**:
```javascript
// Basis-Math
{{ $json.price * 1.19 }}                        // MwSt berechnen
{{ Math.round($json.value * 100) / 100 }}       // Auf 2 Dezimalen
{{ Math.max($json.values) }}                    // Maximum
{{ Math.min($json.values) }}                    // Minimum

// Erweiterte Math
{{ Math.floor($json.price) }}                   // Abrunden
{{ Math.ceil($json.price) }}                    // Aufrunden
{{ ($json.total / $json.count).toFixed(2) }}    // Durchschnitt
```

**Array-Operationen**:
```javascript
// Array-Methoden
{{ $json.items.length }}                        // Länge
{{ $json.items.map(x => x.name) }}             // Namen extrahieren
{{ $json.items.filter(x => x.active) }}        // Filtern
{{ $json.numbers.reduce((a,b) => a + b, 0) }}  // Summe

// Array-Checks
{{ $json.tags.includes('important') }}          // Enthält Element
{{ $json.items.some(x => x.status === 'error') }} // Hat Error
{{ $json.items.every(x => x.valid === true) }}  // Alle valid
```

**Object-Manipulation**:
```javascript
// Object-Operationen
{{ Object.keys($json.user) }}                  // Alle Keys
{{ Object.values($json.user) }}                // Alle Values
{{ Object.entries($json.user) }}               // Key-Value Pairs

// Object merging
{{ Object.assign({}, $json.user, $json.profile) }}
{{ {...$json.user, ...{active: true}} }}       // Spread syntax
```

### 3.4 Bedingte Expressions

**Ternary Operator**:
```javascript
{{ $json.age >= 18 ? 'adult' : 'minor' }}
{{ $json.status === 'active' ? 'enabled' : 'disabled' }}
{{ $json.premium ? $json.price * 0.9 : $json.price }}
```

**Null-Checking**:
```javascript
{{ $json.optional || 'default_value' }}
{{ $json.user?.email || 'no_email' }}
{{ $json.data ? $json.data.length : 0 }}
```

## 4. Praktische Workflow-Patterns mit vollständiger Konfiguration

### 4.1 API-Integration Pattern

**Vollständiger Workflow**:
1. **Webhook Trigger** → 2. **Data Validation** → 3. **API Call** → 4. **Error Handling** → 5. **Response**

**1. Webhook Konfiguration**:
```json
{
  "path": "api-integration",
  "httpMethod": "POST", 
  "authentication": "basicAuth",
  "responseMode": "lastNode"
}
```

**2. IF Node (Validation)**:
```json
{
  "conditions": {
    "conditions": [
      {
        "leftValue": "={{ $json.body.email }}",
        "rightValue": "",
        "operator": {
          "type": "string",
          "operation": "isNotEmpty"
        }
      }
    ]
  }
}
```

**3. HTTP Request (API Call)**:
```json
{
  "method": "POST",
  "url": "https://api.external-service.com/users",
  "sendBody": true,
  "body": {
    "email": "={{ $json.body.email }}",
    "name": "={{ $json.body.name }}",
    "timestamp": "={{ $now }}"
  },
  "options": {
    "timeout": 10000,
    "retry": {
      "enabled": true,
      "maxRetries": 2
    }
  }
}
```

### 4.2 Data Processing Pipeline

**Workflow**: CSV Import → Transform → Filter → Database Insert

**1. HTTP Request (CSV laden)**:
```json
{
  "method": "GET",
  "url": "https://example.com/data.csv",
  "options": {
    "response": {
      "response": {
        "responseFormat": "string"
      }
    }
  }
}
```

**2. Code Node (CSV parsen)**:
```javascript
// CSV zu JSON konvertieren
const csvData = items[0].json.data;
const lines = csvData.split('\n');
const headers = lines[0].split(',');

const jsonData = lines.slice(1)
  .filter(line => line.trim())
  .map(line => {
    const values = line.split(',');
    const obj = {};
    headers.forEach((header, index) => {
      obj[header.trim()] = values[index]?.trim() || '';
    });
    return obj;
  });

return jsonData.map(item => ({ json: item }));
```

**3. Set Node (Daten transformieren)**:
```json
{
  "assignments": {
    "assignments": [
      {
        "name": "fullName",
        "type": "string", 
        "value": "={{ $json.firstName + ' ' + $json.lastName }}"
      },
      {
        "name": "email",
        "type": "string",
        "value": "={{ $json.email.toLowerCase().trim() }}"
      },
      {
        "name": "age",
        "type": "number",
        "value": "={{ parseInt($json.age) || 0 }}"
      }
    ]
  }
}
```

## 5. Error Handling - Detaillierte Strategien

### 5.1 Node-Level Error Handling

**Continue on Fail Setting**:
```json
{
  "continueOnFail": true,
  "onError": "continueRegularOutput"
}
```

### 5.2 Workflow-Level Error Handling

**Error Trigger Workflow**:
```json
{
  "workflowId": "error-handler-workflow",
  "whenSaving": "executeWorkflow"
}
```

### 5.3 Try-Catch Pattern mit IF Node

**Error Detection IF Node**:
```json
{
  "conditions": {
    "conditions": [
      {
        "leftValue": "={{ $json.error }}",
        "rightValue": "",
        "operator": {
          "type": "object",
          "operation": "exists"
        }
      }
    ]
  }
}
```

## 6. Advanced Workflow Patterns

### 6.1 Batch Processing mit Loop

**Split in Batches Node**:
```json
{
  "batchSize": 10,
  "options": {
    "reset": false
  }
}
```

### 6.2 Retry Logic Pattern

**Code Node (Custom Retry)**:
```javascript
const maxRetries = 3;
let retryCount = 0;
let success = false;
let result = null;

while (retryCount < maxRetries && !success) {
  try {
    // Simulation einer API-Anfrage
    const response = await fetch('https://api.example.com/data');
    result = await response.json();
    success = true;
  } catch (error) {
    retryCount++;
    if (retryCount < maxRetries) {
      // Exponential backoff
      await new Promise(resolve => 
        setTimeout(resolve, Math.pow(2, retryCount) * 1000)
      );
    }
  }
}

return [{
  json: {
    success,
    retryCount,
    result: result || null
  }
}];
```

## 7. Database Integration - Vollständige Beispiele

### 7.1 MySQL Node Konfiguration

**Connection Setup**:
```json
{
  "host": "localhost",
  "port": 3306,
  "database": "myapp",
  "user": "dbuser",
  "password": "{{ $credentials.mysql.password }}",
  "ssl": false
}
```

**Query Beispiele**:

**SELECT mit Parametern**:
```sql
SELECT * FROM users 
WHERE email = '{{ $json.email }}' 
AND created_at > '{{ DateTime.now().minus({days: 30}).toFormat('yyyy-MM-dd') }}'
LIMIT 10;
```

**INSERT mit Multiple Records**:
```sql
INSERT INTO orders (user_id, product_id, quantity, price) VALUES
{{ $input.all().map(item => 
  `(${item.json.userId}, ${item.json.productId}, ${item.json.quantity}, ${item.json.price})`
).join(',') }};
```

**UPDATE mit Conditions**:
```sql
UPDATE users 
SET last_login = '{{ $now }}',
    login_count = login_count + 1
WHERE id = {{ $json.userId }};
```

## 8. File Operations - Detaillierte Konfiguration

### 8.1 Read/Write Files Node

**File lesen**:
```json
{
  "operation": "read",
  "filePath": "/path/to/file.json",
  "encoding": "utf8",
  "fileFormat": "json"
}
```

**File schreiben**:
```json
{
  "operation": "write",
  "filePath": "/output/{{ DateTime.now().toFormat('yyyy-MM-dd') }}.json",
  "fileContent": "={{ JSON.stringify($input.all()) }}",
  "encoding": "utf8"
}
```

## 9. Monitoring und Debugging

### 9.1 Debug Helper Node
```json
{
  "debugToConsole": true,
  "debugToFile": true,
  "logLevel": "info"
}
```

### 9.2 Execution Logging Code Pattern
```javascript
// Logging in Code Node
console.log('Processing item:', $itemIndex);
console.log('Input data:', JSON.stringify($json, null, 2));

// Workflow-weites Logging
return [{
  json: {
    ...$json,
    debug: {
      nodeId: $workflow.id,
      executionId: $execution.id,
      timestamp: $now,
      itemIndex: $itemIndex
    }
  }
}];
```

## 10. Performance Optimization Guidelines

### 10.1 Batch Processing Strategies

```javascript
// Effiziente Batch-Verarbeitung
const BATCH_SIZE = 100;
const batches = [];

for (let i = 0; i < items.length; i += BATCH_SIZE) {
  batches.push(items.slice(i, i + BATCH_SIZE));
}

const results = [];
for (const batch of batches) {
  // API-Call für jeden Batch
  const batchResult = await processBatch(batch);
  results.push(...batchResult);
}

return results.map(item => ({ json: item }));
```

### 10.2 Memory Management

```javascript
// Memory-effiziente Datenverarbeitung
const processedItems = [];

for (let i = 0; i < items.length; i++) {
  const item = items[i];
  
  // Verarbeitung
  const processed = processItem(item.json);
  processedItems.push({ json: processed });
  
  // Memory cleanup für große Datasets
  if (i % 1000 === 0) {
    // Zwischenspeicherung oder Garbage Collection
    console.log(`Processed ${i} items`);
  }
}

return processedItems;
```

## 11. Security Best Practices - Implementation

### 11.1 Input Validation Pattern

```javascript
// Input Validation in Code Node
function validateInput(data) {
  const errors = [];
  
  // Email validation
  if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('Invalid email format');
  }
  
  // Required fields
  const required = ['name', 'email', 'phone'];
  required.forEach(field => {
    if (!data[field] || data[field].trim() === '') {
      errors.push(`${field} is required`);
    }
  });
  
  // Data sanitization
  const sanitized = {
    name: data.name?.trim().substring(0, 100),
    email: data.email?.toLowerCase().trim(),
    phone: data.phone?.replace(/[^\d+\-\s]/g, '')
  };
  
  return { isValid: errors.length === 0, errors, data: sanitized };
}

const validation = validateInput($json);
return [{ json: validation }];
```

## 12. Real-World Workflow Examples

### 12.1 E-Commerce Order Processing

**Kompletter Workflow mit allen Konfigurationen**:

1. **Webhook** (Order received)
2. **Validation** (Order data)
3. **Inventory Check** (Database query)
4. **Payment Processing** (External API)  
5. **Order Confirmation** (Email)
6. **Inventory Update** (Database)

**Detaillierte Node-Konfigurationen für jeden Schritt verfügbar auf Anfrage**

### 12.2 Social Media Monitoring

1. **Schedule Trigger** (Every 15 minutes)
2. **Multiple HTTP Requests** (Twitter, Facebook APIs)
3. **Data Aggregation** (Code Node)
4. **Sentiment Analysis** (AI Node)
5. **Database Storage** (PostgreSQL)
6. **Alert System** (Slack/Email)

Diese detaillierte Knowledge Base bietet der KI alle notwendigen Informationen, um n8n Workflows präzise zu erstellen, zu konfigurieren und zu optimieren. Jeder Node-Typ ist mit vollständigen Konfigurationsbeispielen und praktischen Use Cases dokumentiert.`
// Example: convert the incoming query to uppercase and return it
return text;